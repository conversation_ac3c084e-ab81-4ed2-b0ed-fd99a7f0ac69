import { <PERSON>mpo<PERSON>, After<PERSON>iew<PERSON><PERSON><PERSON>, <PERSON>Child, ViewChildren, QueryList, ElementRef, OnInit, ChangeDetectorRef, OnDestroy, AfterViewChecked } from '@angular/core';
import { AuthService } from '@auth0/auth0-angular';
import Quill from 'quill';
import { formatDate } from '@angular/common';
import { DiscussionCommentsService } from '../../service/ticket-view.service';
import { HelpCentreTicket, User, TabConfig } from './data';
import { Subscription } from 'rxjs';
import { UserService } from '../../service/user.service';
import { ActivatedRoute } from '@angular/router';

interface TicketComment {
  user: string;
  text: string;
  date: string;
}

@Component({
  selector: 'app-ticket-view',
  templateUrl: './ticket-view.component.html',
  styleUrls: ['./ticket-view.component.scss']
})
export class TicketViewComponent implements AfterViewInit, AfterViewChecked, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy {
  tabs: TabConfig[] = [
    { id: 'all', header: 'All Tickets', filterKey: 'all' },
    { id: 'my-tickets', header: 'My Tickets', filterKey: 'user' },
  ];
  tickets: (HelpCentreTicket & { comments?: TicketComment[] })[] = [];
  selectedTab: TabConfig = this.tabs[0];
  selectedTicketId: string | null = null;
  sidebarOpen: boolean = false;
  sidebarMode: 'edit' | 'new' | null = null;
  editTitle: string = '';
  editMessage: string = '';
  comment: string = '';
  selectedTicket: any | null = null;
  currentUser: any = { userName: '', userInitials: '', isCurrentUser: true };
  comments: any[] = [];
  editorContent: string = '';

  // Form validation states
  formErrors: { title?: string; message?: string } = {};
  isSubmitting: boolean = false;

  // Mention functionality properties
  choices: User[] = [];

  // Quill editor configuration
  quillModules = {
    toolbar: {
      container: [
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        ['superscript', 'subscript'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'align': [] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'indent': '-1'}, { 'indent': '+1' }],
        ['link', 'image', 'file'],
        ['clean']
      ],
      handlers: {
        image: () => this.selectLocalImage(),
        file: () => this.selectLocalFile(),
        link: () => this.insertLink(),
        superscript: () => this.toggleSuperscript(),
        subscript: () => this.toggleSubscript()
      }
    },
    clipboard: {
      matchVisual: false
    },
    keyboard: {
      bindings: {
        tab: {
          key: 9,
          handler: function() {
            return true;
          }
        }
      }
    }
  };

  quillEditors: { [key: string]: any } = {};
  mentionDropdowns: { [key: string]: any } = {};

  // Add these properties to the class
  private mentionAtIndex: number | null = null;
  private mentionSearchTerm: string = '';

  commentSectionLoading = false;
  isCommentSubmitting = false;

  // Add state for editing comments
  editingCommentId: number | null = null;
  editingCommentContent: string = '';

  // Add state for replying to comments
  replyingToCommentId: number | string | null = null;
  replyContent: { [commentId: string]: string } = {};

  // Add state for collapsed comment groups
  collapsedGroups: Set<string> = new Set();

  // Add grouped comments as a property instead of getter
  groupedComments: { isDiscussion: boolean; comments: any[] }[] = [];

  currentProjectId: string | null = null;
  currentGroupId: string | null = null;
  selectedSupplier: any = null;
  subscriptionId: string | null = null;
  projectLabels: any[] = [];
  groupLabels: any[] = [];
  groupMembers: any[] = [];
  projectMembers: any[] = [];
  public privateToken: string = '';
  selectedLabel: string = '';
  originalLabel: string = '';

  // Loader for ticket list
  ticketListLoading: boolean = false;
  ticketListLoadedOnce: boolean = false;

  // Check if current user can edit a comment
  canEditComment(comment: any): boolean {
    // For now, allow editing if the comment has an id (meaning it's a real comment)
    // In a real application, you would check if the current user is the author
    return comment && comment.id && this.currentUser && this.currentUser.userName;
  }

  // Check if current user can delete a comment
  canDeleteComment(comment: any): boolean {
    // For now, allow deletion if the comment has an id (meaning it's a real comment)
    // In a real application, you would check if the current user is the author or has admin rights
    return comment && comment.id && this.currentUser && this.currentUser.userName;
  }

  onEditorCreated(quill: any, context: string) {
    this.quillEditors[context] = quill;
    this.setupMentionFunctionality(quill, context);
    if (context === 'main') {
      this.updateEditorState();
    }
  }

  // Update editor state based on whether it should be enabled
  updateEditorState() {
    const mainEditor = this.quillEditors['main'];
    if (mainEditor) {
      if (!this.isCommentEditorEnabled) {
        mainEditor.disable();
      } else {
        mainEditor.enable();
      }
    }
  }

  // Setup mention functionality within Quill
  setupMentionFunctionality(quill: any, context: string) {
    this.createMentionDropdown(context);

    // Listen for text changes
    quill.on('text-change', (delta, oldDelta, source) => {
      if (source === 'user') {
        this.handleTextChange(quill, context);
      }
    });

    // Listen for keydown events
    quill.on('keydown', (event) => {
      this.handleKeydown(event, quill, context);
      this.handleEditModeKeydown(event);
    });
  }

  // Create mention dropdown
  createMentionDropdown(context: string) {
    // Remove existing dropdown if any
    if (this.mentionDropdowns[context]) {
      this.mentionDropdowns[context].remove();
    }

    // Create dropdown element
    const dropdown = document.createElement('div');
    dropdown.className = 'quill-mention-dropdown';
    dropdown.style.cssText = `
      position: absolute;
      background: white;
      border: 1px solid #d0d7de;
      border-radius: 8px;
      box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2);
      max-height: 200px;
      overflow-y: auto;
      z-index: 10000;
      display: none;
      min-width: 200px;
    `;

    document.body.appendChild(dropdown);
    this.mentionDropdowns[context] = dropdown;
  }

  // Handle text changes to detect @ mentions
  handleTextChange(quill: any, context: string) {
    const text = quill.getText();
    const cursorPosition = quill.getSelection()?.index || 0;

    // Find @ symbol before cursor
    const beforeCursor = text.substring(0, cursorPosition);
    const atIndex = beforeCursor.lastIndexOf('@');

    if (atIndex !== -1 && atIndex < cursorPosition) {
      const searchTerm = beforeCursor.substring(atIndex + 1);
      this.showMentionDropdown(searchTerm, atIndex, quill, context);
    } else {
      this.hideMentionDropdown(context);
    }
  }

  // Handle keydown events
  handleKeydown(event: any, quill: any, context: string) {
    const dropdown = this.mentionDropdowns[context];
    if (dropdown && dropdown.style.display !== 'none') {
      if (event.key === 'ArrowDown' || event.key === 'ArrowUp' || event.key === 'Enter' || event.key === 'Escape') {
        event.preventDefault();
        this.handleMentionKeydown(event, quill, context);
      }
    }
  }

  // Handle keyboard shortcuts in edit mode
  handleEditModeKeydown(event) {
    if (this.editingCommentId) {
      // Ctrl/Cmd + Enter to save
      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        event.preventDefault();
        const currentComment = this.comments.find(c => c.id === this.editingCommentId);
        if (currentComment && this.editingCommentContent.trim()) {
          this.saveEditedComment(currentComment);
        }
      }
      // Escape to cancel
      if (event.key === 'Escape') {
        event.preventDefault();
        this.cancelEditComment();
      }
    }
  }

  // Show mention dropdown
  showMentionDropdown(searchTerm: string, atIndex: number, quill: any, context: string) {
    // Filter from the full choices list, do not overwrite it
    const filteredUsers = this.choices.filter(user =>
      user.name && user.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (filteredUsers.length === 0) {
      this.hideMentionDropdown(context);
      return;
    }

    quill.mentionAtIndex = atIndex;
    quill.mentionSearchTerm = searchTerm;
    this.renderMentionDropdown(filteredUsers, quill, context); // Pass filtered users
    this.positionMentionDropdown(atIndex, quill, context);
    this.mentionDropdowns[context].style.display = 'block';
    // Add document click listener
    document.addEventListener('mousedown', (event) => this.mentionDocumentClickHandler(event, quill, context), true);
  }

  // Hide mention dropdown
  hideMentionDropdown(context: string) {
    const dropdown = this.mentionDropdowns[context];
    if (dropdown) {
      dropdown.style.display = 'none';
    }
    // Remove document click listener
    document.removeEventListener('mousedown', (event) => this.mentionDocumentClickHandler(event, null, context), true);
  }

  // Render mention dropdown content
  renderMentionDropdown(filteredUsers: any[], quill: any, context: string) {
    const dropdown = this.mentionDropdowns[context];
    dropdown.innerHTML = '';

    filteredUsers.forEach((user, index) => {
      const item = document.createElement('div');
      item.className = 'mention-item';
      item.style.cssText = `
        padding: 8px 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 12px;
        transition: background-color 0.15s ease;
      `;

      item.innerHTML = `
        <div class="mention-avatar" style="
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: ${this.getAvatarColor(user.name)};
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 12px;
        ">${this.getAvatarText(user.name)}</div>
        <div class="mention-info">
          <div class="mention-name" style="font-weight: 600; color: #24292f; font-size: 14px;">${user.name}</div>
          <div class="mention-email" style="font-size: 12px; color: #656d76;">${user.email || ''}</div>
        </div>
      `;

      item.addEventListener('click', () => this.selectMention(user, quill, context));
      item.addEventListener('mouseenter', () => {
        item.style.backgroundColor = '#f6f8fa';
      });
      item.addEventListener('mouseleave', () => {
        item.style.backgroundColor = 'transparent';
      });

      dropdown.appendChild(item);
    });
  }

  // Position mention dropdown
  positionMentionDropdown(atIndex: number, quill: any, context: string) {
    const editorBounds = quill.container.getBoundingClientRect();
    const range = quill.getBounds(atIndex);
    // Always position below the mention, even if it overflows the editor
    const dropdown = this.mentionDropdowns[context];
    dropdown.style.left = `${editorBounds.left + range.left}px`;
    dropdown.style.top = `${editorBounds.top + range.bottom + 5}px`;
    dropdown.style.bottom = 'auto'; // Ensure not set
    dropdown.style.maxHeight = '200px'; // Prevents it from growing too tall
    dropdown.style.overflowY = 'auto';
  }

  // Add document click listener to close mention dropdown when clicking outside
  private mentionDocumentClickHandler = (event: MouseEvent, quill: any, context: string) => {
    const dropdown = this.mentionDropdowns[context];
    if (!dropdown) return;
    const target = event.target as Node;
    // If click is outside the dropdown and outside the editor, close it
    if (
      !dropdown.contains(target) &&
      (!quill || !quill.container.contains(target))
    ) {
      this.hideMentionDropdown(context);
    }
  };

  // Handle mention dropdown keydown
  handleMentionKeydown(event: any, quill: any, context: string) {
    const dropdown = this.mentionDropdowns[context];
    const items = dropdown.querySelectorAll('.mention-item');
    const currentIndex = Array.from(items).findIndex(item => (item as HTMLElement).classList.contains('selected'));

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
        this.selectMentionItem(items, nextIndex);
        break;
      case 'ArrowUp':
        event.preventDefault();
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
        this.selectMentionItem(items, prevIndex);
        break;
      case 'Enter':
        event.preventDefault();
        if (currentIndex >= 0) {
          this.selectMention(this.choices[currentIndex], quill, context);
        }
        break;
      case 'Escape':
        event.preventDefault();
        this.hideMentionDropdown(context);
        break;
    }
  }

  // Select mention item visually
  selectMentionItem(items: NodeListOf<Element>, index: number) {
    items.forEach(item => (item as HTMLElement).classList.remove('selected'));
    if (items[index]) {
      (items[index] as HTMLElement).classList.add('selected');
      (items[index] as HTMLElement).style.backgroundColor = '#f6f8fa';
    }
  }

  // Select mention
  selectMention(user: User, quill: any, context: string) {
    if (quill.mentionAtIndex !== null) {
      const atIndex = quill.mentionAtIndex;
      const searchTerm = quill.mentionSearchTerm;
      const replacement = `@${user.name} `;

      // Focus and set selection
      quill.focus();
      quill.setSelection(atIndex + replacement.length, 0, 'user');

      // Delete the @searchTerm
      quill.deleteText(atIndex, searchTerm.length + 1, 'user');

      // Insert the mention (only blue color, no background or bold)
      quill.insertText(atIndex, replacement, {
        'color': '#0969da'
      }, 'user');

      // Move the cursor after the mention
      quill.setSelection(atIndex + replacement.length, 0, 'user');

      // Reset formatting after the mention
      quill.format('color', false, 'user');
      quill.format('background', false, 'user');
      quill.format('bold', false, 'user');

      // Reset
      quill.mentionAtIndex = null;
      quill.mentionSearchTerm = '';
    }
    this.hideMentionDropdown(context);
  }

  // Helper to check if a file is an image
  isImage(filename: string): boolean {
    return /\.(png|jpe?g|gif|bmp|svg)$/i.test(filename);
  }

  // Store selected files before sending comment
  pendingFiles: { name: string; file_path: string; type: string; uploading?: boolean; error?: string }[] = [];

  async selectLocalImage() {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();
    input.onchange = async () => {
      const file = input.files[0];
      if (file && this.subscriptionId) {
        const pendingIndex = this.pendingFiles.length;
        this.pendingFiles.push({ name: file.name, file_path: '', type: file.type, uploading: true });
        try {
          const uploadUrlResp = await this.discussionCommentsService.getAttachmentUploadSignedUrl(this.subscriptionId, file.name).toPromise();
          await this.discussionCommentsService.uploadFileToSignedUrl(uploadUrlResp.signed_url, file).toPromise();
          this.pendingFiles[pendingIndex] = { name: file.name, file_path: uploadUrlResp.file_path, type: file.type };
        } catch (err) {
          this.pendingFiles[pendingIndex] = { name: file.name, file_path: '', type: file.type, error: 'Upload failed' };
        }
        this.cdr.detectChanges();
      }
    };
  }

  async selectLocalFile() {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', '.pdf,.doc,.docx,.txt,.xls,.xlsx,.csv,.zip,.rar,.7z,.json');
    input.click();
    input.onchange = async () => {
      const file = input.files[0];
      if (file && this.subscriptionId) {
        const pendingIndex = this.pendingFiles.length;
        this.pendingFiles.push({ name: file.name, file_path: '', type: file.type, uploading: true });
        try {
          const uploadUrlResp = await this.discussionCommentsService.getAttachmentUploadSignedUrl(this.subscriptionId, file.name).toPromise();
          await this.discussionCommentsService.uploadFileToSignedUrl(uploadUrlResp.signed_url, file).toPromise();
          this.pendingFiles[pendingIndex] = { name: file.name, file_path: uploadUrlResp.file_path, type: file.type };
        } catch (err) {
          this.pendingFiles[pendingIndex] = { name: file.name, file_path: '', type: file.type, error: 'Upload failed' };
        }
        this.cdr.detectChanges();
      }
    };
  }

  insertLink() {
    const url = prompt('Enter URL:');
    if (url) {
      const range = this.quillEditors['main'].getSelection();
      if (range) {
        if (range.length > 0) {
          // If text is selected, make it a link
          this.quillEditors['main'].format('link', url);
        } else {
          // If no text is selected, insert the URL as a link
          this.quillEditors['main'].insertText(range.index, url, 'link', url);
        }
      }
    }
  }

  toggleSuperscript() {
    if (this.quillEditors['main']) {
      const range = this.quillEditors['main'].getSelection();
      if (range) {
        const format = this.quillEditors['main'].getFormat(range);
        const isSuperscript = format.script === 'super';

        if (isSuperscript) {
          // Remove superscript
          this.quillEditors['main'].format('script', false);
        } else {
          // Apply superscript
          this.quillEditors['main'].format('script', 'super');
        }
      }
    }
  }

  toggleSubscript() {
    if (this.quillEditors['main']) {
      const range = this.quillEditors['main'].getSelection();
      if (range) {
        const format = this.quillEditors['main'].getFormat(range);
        const isSubscript = format.script === 'sub';

        if (isSubscript) {
          // Remove subscript
          this.quillEditors['main'].format('script', false);
        } else {
          // Apply subscript
          this.quillEditors['main'].format('script', 'sub');
        }
      }
    }
  }

  @ViewChild('chatList') chatList!: ElementRef;
  @ViewChildren('lastCommentDiv') lastCommentDivs!: QueryList<ElementRef>;
  @ViewChildren('parentCard') parentCardElements!: QueryList<ElementRef>;
  parentOffsets: number[] = [];
  private lastCount = 0;

  constructor(
    private cdr: ChangeDetectorRef,
    private discussionCommentsService: DiscussionCommentsService,
    public auth: AuthService,
    private userService: UserService,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    // Fetch subscription ID from query params
    this.route.queryParams.subscribe(params => {
      this.subscriptionId = params['sub'] || null;
      // Fetch current user's project and group id
      if (this.subscriptionId) {
        this.userService.me(this.subscriptionId).subscribe({
          next: (resp) => {
            this.currentProjectId = resp.result?.retailer_git_project_id || null;
            this.currentGroupId = resp.result?.retailer_git_group_id || null;
            this.loadTickets();
            this.loadLabelsAndMembers();
          },
          error: () => {
            this.loadTickets(); // fallback
          }
        });
      } else {
        this.loadTickets(); // fallback if no subscriptionId
      }
    });
    this.loadUsers();
    // Fetch the current user profile from Auth0
    this.auth.user$.subscribe(profile => {
      if (profile) {
        this.currentUser.userName =
          profile.name && profile.name.trim() !== ''
            ? profile.name.trim()
            : profile.username && profile.username.includes('@')
              ? profile.username.split('@')[0]
              : profile.username || profile.nickname || profile.email || '';
        this.currentUser.email = profile.email || '';
        this.currentUser.id = 2;
      }
    });

    // Register custom file icon for Quill
    const icons = Quill.import('ui/icons');
    icons['file'] = `<svg viewBox="0 0 18 18"><path d="M14.5 8.5v3.5a4.5 4.5 0 0 1-9 0v-7a2.5 2.5 0 0 1 5 0v7a.5.5 0 0 1-1 0v-7a1.5 1.5 0 0 0-3 0v7a3.5 3.5 0 0 0 7 0V8.5a.5.5 0 0 1 1 0z"/></svg>`;

    // Register custom superscript icon
    icons['superscript'] = `<svg viewBox="0 0 18 18">
      <text x="2" y="12" font-family="Arial" font-size="10" fill="currentColor">X</text>
      <text x="8" y="8" font-family="Arial" font-size="6" fill="currentColor">2</text>
    </svg>`;

    // Register custom subscript icon
    icons['subscript'] = `<svg viewBox="0 0 18 18">
      <text x="2" y="12" font-family="Arial" font-size="10" fill="currentColor">X</text>
      <text x="8" y="16" font-family="Arial" font-size="6" fill="currentColor">2</text>
    </svg>`;
  }

  loadTickets() {
    const projectId = this.getCurrentProjectId();
    if (!this.subscriptionId || !projectId) return;
    this.ticketListLoading = true; // Start loading
    this.discussionCommentsService.getTickets(this.subscriptionId, projectId).subscribe({
      next: (apiTickets) => {
        if (!Array.isArray(apiTickets)) {
          this.tickets = [];
          this.ticketListLoading = false;
          this.ticketListLoadedOnce = true;
          return;
        }
        this.tickets = apiTickets.map(ticket => this.mapApiTicketToLocalFormat(ticket))
          .filter(Boolean) as (HelpCentreTicket & { comments?: TicketComment[] })[];
        this.tickets.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
        this.ticketListLoading = false; // Stop loading
        this.ticketListLoadedOnce = true;
      },
      error: (err) => {
        this.tickets = [];
        this.ticketListLoading = false; // Stop loading
        this.ticketListLoadedOnce = true;
      }
    });
  }

  loadUsers() {
    // Example: this.userService.getUsers().subscribe(users => { this.mentionUsers = users; });
    // If you don't have a users API, leave this empty or remove it
  }

  loadLabelsAndMembers() {
    const subscriptionId = this.subscriptionId;
    const projectId = this.currentProjectId;
    const groupId = this.currentGroupId;
    const privateToken = this.privateToken;

    if (subscriptionId && projectId) {
      this.discussionCommentsService.getProjectLabels(subscriptionId, projectId, privateToken)
        .subscribe(
          labels => {
            this.projectLabels = labels;
          },
          err => {}
        );
      this.discussionCommentsService.getProjectMembers(subscriptionId, projectId, privateToken)
        .subscribe(
          members => {
            this.projectMembers = members;
            // Fallback: if no group members, use project members for mentions
            if (!this.choices.length && Array.isArray(members)) {
              this.choices = members.map(m => ({
                name: m.name || m.username || '',
                username: m.username || m.name || '',
                email: m.public_email || '',
                avatar: m.avatar_url || '',
                id: m.id
              }));
            }
          },
          err => {}
        );
    }
    if (subscriptionId && groupId) {
      this.discussionCommentsService.getGroupLabels(subscriptionId, groupId, privateToken)
        .subscribe(
          labels => {
            this.groupLabels = labels;
          },
          err => {}
        );
      this.discussionCommentsService.getGroupMembers(subscriptionId, groupId, privateToken)
        .subscribe(
          members => {
            this.groupMembers = members;
            // Use group members for mentions if available
            if (Array.isArray(members) && members.length > 0) {
              this.choices = members.map(m => ({
                name: m.name || m.username || '',
                username: m.username || m.name || '',
                email: m.public_email || '',
                avatar: m.avatar_url || '',
                id: m.id
              }));
            }
          },
          err => {}
        );
    }
  }

  ngAfterViewInit() {
    this.calculateParentOffsets();
  }

  ngAfterViewChecked() {
    if (this.parentCardElements.length !== this.lastCount) {
      this.calculateParentOffsets();
      this.lastCount = this.parentCardElements.length;
    }
  }

  calculateParentOffsets() {
    setTimeout(() => {
      this.parentOffsets = this.parentCardElements.map(el => el.nativeElement.offsetTop);
      this.cdr.detectChanges();
    });
  }

  get allTicketsCount(): number {
    return this.tickets.length;
  }

  get myTicketsCount(): number {
    if (!this.currentUser || !this.currentUser.userName) {
      return 0;
    }
    return this.tickets.filter(ticket => ticket.user === this.currentUser.userName).length;
  }

  get filteredTickets(): any[] {
    if (this.selectedTab.filterKey === 'user') {
      if (!this.currentUser || !this.currentUser.userName) {
        return [];
      }
      return this.tickets.filter(ticket => ticket.user === this.currentUser.userName);
    }
    return this.tickets;
  }

  get isTicketOwner(): boolean {
    if (!this.selectedTicket || !this.currentUser || !this.currentUser.userName) {
      return false;
    }
    // Check both possible author fields for robustness
    return (
      this.selectedTicket.author?.username === this.currentUser.userName ||
      this.selectedTicket.user === this.currentUser.userName
    );
  }

  get isCommentEditorEnabled(): boolean {
    if (this.sidebarMode === 'new') {
      return this.editTitle.trim().length > 0;
    }
    return true; // Always enabled for edit mode
  }

  get selectedTicketComments(): TicketComment[] {
    if (!this.selectedTicket) return [];
    const ticket = this.tickets.find(t => t.id === this.selectedTicket!.id);
    return ticket?.comments || [];
  }

  selectTab(tab: TabConfig) {
    this.selectedTab = tab;
    this.selectedTicketId = null;
  }

  onTicketClick(ticket: any) {
    this.sidebarOpen = true;
    this.sidebarMode = 'edit';
    this.isSubmitting = true;
    this.commentSectionLoading = true;

    const projectId = this.getCurrentProjectId();
    const issueIid = ticket.iid;
    if (!this.subscriptionId || !projectId || !issueIid) return;
    const details$ = this.discussionCommentsService.getTicketDetails(this.subscriptionId, projectId, issueIid);
    const discussions$ = this.discussionCommentsService.getDiscussions(this.subscriptionId, projectId, issueIid);

    import('rxjs').then(rxjs => {
      rxjs.forkJoin([details$, discussions$]).subscribe({
        next: (results: [any, any[]]) => {
          const [detailedTicket, discussions] = results;
          this.selectedTicket = { ...detailedTicket, ...ticket };
          this.selectedTicketId = detailedTicket.id;
          this.editTitle = detailedTicket.title;
          this.editMessage = detailedTicket.description;
          // Pre-select label if present
          let label = '';
          if (Array.isArray(detailedTicket.labels)) {
            label = detailedTicket.labels.length > 0 ? detailedTicket.labels[0] : '';
          } else if (typeof detailedTicket.labels === 'string') {
            label = detailedTicket.labels;
          } else if (detailedTicket.label) {
            label = detailedTicket.label;
          }
          this.selectedLabel = label;
          this.originalLabel = label;

          const ticketCreationEvent = {
            type: 'ticket-created',
            userName: this.selectedTicket.author.name,
            userInitials: this.getAvatarText(this.selectedTicket.author.name),
            avatarUrl: this.selectedTicket.author.avatar_url,
            text: `Ticket \"${this.selectedTicket.title}\" was created`,
            timestamp: this.selectedTicket.created_at,
            compact: true
          };
          let discussionNotes: any[] = [];
          if (discussions && discussions.length > 0) {
            this.selectedTicket.discussionId = discussions[0].id;
            // Flatten all notes from all discussions
            discussionNotes = this.flattenNotesFromDiscussions(discussions);
          }
          // Use ONLY real API data - no hardcoded system activities
          // The API already provides system activities with "system": true

          // Combine all timeline items and sort chronologically
          const allTimelineItems = [ticketCreationEvent, ...discussionNotes];
          this.comments = allTimelineItems.sort((a, b) =>
            new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );
          this.updateGroupedComments();
          // this.renderLemonadeTimeline();

          this.isSubmitting = false;
          this.commentSectionLoading = false;
          this.cdr.detectChanges();
          setTimeout(() => this.scrollToLastComment(), 100);

          // Removed polling logic
          // this.startCommentPolling(this.selectedTicket);
        },
        error: (err) => {
          this.isSubmitting = false;
          this.commentSectionLoading = false;
          this.closeSidebar();
        }
      });
    });
  }

  onRaiseTicket() {
    this.selectedTicketId = null;
    this.selectedTicket = null;
    this.editTitle = '';
    this.editMessage = '';
    this.comment = '';
    this.editorContent = '';
    this.pendingFiles = [];
    this.formErrors = {};
    this.isSubmitting = false;
    this.comments = [];
    this.groupedComments = [];
    this.collapsedGroups.clear();
    this.sidebarMode = 'new';
    this.sidebarOpen = true;
    this.selectedLabel = '';
    setTimeout(() => this.scrollToLastComment(), 0);
  }

  closeSidebar() {
    this.sidebarOpen = false;
    this.sidebarMode = null;
    this.selectedTicket = null;
    this.selectedTicketId = null;
    this.editTitle = '';
    this.editMessage = '';
    this.comment = '';
    this.editorContent = '';
    this.pendingFiles = [];
    this.formErrors = {};
    this.isSubmitting = false;
    this.comments = [];
    this.groupedComments = [];
    this.collapsedGroups.clear();
    // Removed polling logic
    // this.stopCommentPolling();
  }

  // Validate form fields
  private validateForm(): boolean {
    this.clearValidationErrors();
    let valid = true;
    if (!this.editTitle.trim()) {
      this.formErrors.title = 'Title is required.';
      valid = false;
    }
    // Description is now optional, so no validation here
    return valid;
  }

  // Generate unique ticket ID
  private generateTicketId(): string {
    const existingIds = this.tickets.map(t => parseInt(t.ticketId.replace('AD24AR', '')));
    const maxId = Math.max(0, ...existingIds);
    const newId = maxId + 1;
    return `AD24AR${newId.toString().padStart(3, '0')}`;
  }

  // Clear validation errors when user starts typing
  onFieldChange(field: 'title' | 'message') {
    // Clear validation error for this field
    if (this.formErrors[field]) {
      delete this.formErrors[field];
    }

    // Update editor state when title changes
    if (field === 'title') {
      this.updateEditorState();
    }
  }

  // Clear all validation errors
  clearValidationErrors() {
    this.formErrors = {};
  }

  // Handle cancel with unsaved changes check
  onCancel() {
    let hasChanges = false;
    if (this.sidebarMode === 'edit' && this.selectedTicket) {
      hasChanges = (
        this.editTitle.trim() !== (this.selectedTicket.title || '').trim() ||
        this.editMessage.trim() !== (this.selectedTicket.description || '').trim()
      );
    } else if (this.sidebarMode === 'new') {
      hasChanges = (
        this.editTitle.trim() !== '' ||
        this.editMessage.trim() !== ''
      );
    }

    if (hasChanges && !this.isSubmitting) {
      if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        this.closeSidebar();
      }
    } else {
      this.closeSidebar();
    }
  }

  saveTicket() {
    // Allow label change to be saved even if other fields are not changed
    if (this.sidebarMode === 'edit' && this.isLabelChanged) {
      this.isSubmitting = true;
      this.selectedTicket.label = this.selectedLabel;
      this.selectedTicket.updated_at = new Date().toISOString();
      this.originalLabel = this.selectedLabel;
      this.isSubmitting = false;
      this.cdr.detectChanges();
      this.closeSidebar(); // Close sidebar after saving label
      return;
    }
    if (!this.validateForm()) return;
    this.isSubmitting = true;

    const projectId = this.getCurrentProjectId();
    if (!this.subscriptionId || !projectId) return;

    if (this.sidebarMode === 'new') {
      // Create new ticket
      const newTicket: HelpCentreTicket = {
        id: this.generateTicketId(),
        iid: Date.now(),
        project_id: Number(this.currentProjectId) || 0,
        type: 'added',
        ticketId: 'TCKT-' + Math.floor(Math.random() * 100000),
        title: this.editTitle,
        message: this.editMessage || '',
        description: this.editMessage || '',
        user: this.currentUser.userName,
        date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'open',
        read: false,
        label: this.selectedLabel || '',
      };
      this.tickets.unshift(newTicket);
      this.selectedTicket = newTicket;
      this.selectedTicketId = newTicket.id;
      this.sidebarMode = 'edit';
      this.isSubmitting = false;
      this.cdr.detectChanges();
      this.closeSidebar(); // Close sidebar after saving new ticket
    } else if (this.sidebarMode === 'edit' && this.selectedTicket) {
      // Store old values for system activity generation
      const oldTitle = this.selectedTicket.title;
      const oldLabel = this.selectedTicket.label;
      const oldAssignee = this.selectedTicket.assignee;
      const oldMilestone = this.selectedTicket.milestone;
      const oldDueDate = this.selectedTicket.due_date;

      // Update existing ticket
      this.selectedTicket.title = this.editTitle;
      this.selectedTicket.message = this.editMessage;
      this.selectedTicket.description = this.editMessage;
      this.selectedTicket.label = this.selectedLabel;
      this.selectedTicket.updated_at = new Date().toISOString();

      // Generate system activities for changes
      if (oldTitle !== this.editTitle) {
        this.generateSystemActivityForTitleChange(oldTitle, this.editTitle);
      }
      if (oldLabel !== this.selectedLabel) {
        this.generateSystemActivityForLabelChange(oldLabel, this.selectedLabel);
      }
      // Note: Assignee, milestone, and due date changes would be handled
      // when those UI components are implemented

      this.isSubmitting = false;
      this.cdr.detectChanges();
      this.closeSidebar(); // Close sidebar after saving edit
    }
  }

  // Helper function to map ticket data to avoid code duplication
  mapApiTicketToLocalFormat(ticket: any): HelpCentreTicket | null {
    if (!ticket) return null;
    // Handle label as string or array
    let label = '';
    if (Array.isArray(ticket.labels)) {
      label = ticket.labels.length > 0 ? ticket.labels.join(', ') : '';
    } else if (typeof ticket.labels === 'string') {
      label = ticket.labels;
    }
    return {
      id: ticket.id.toString(),
      iid: ticket.iid,
      project_id: ticket.project_id,
      title: ticket.title || 'No Title',
      message: ticket.description || '',
      description: ticket.description || '',
      user: ticket.author?.name || ticket.author?.username || ticket.user || '',
      author: ticket.author,
      date: ticket.created_at,
      updated_at: ticket.updated_at,
      status: ticket.state === 'closed' ? 'closed' : 'open',
      read: ticket.state ? !ticket.state.includes('opened') : true,
      ticketId: `AD24AR${ticket.iid}`,
      type: 'comment',
      label,
    };
  }

  // Optionally, add a method to log what is being used for display
  logDisplayAuthor(item: any) {
    const display = item.author?.name || item.author?.username || item.userName || item.user;
    // console.log('Display author for item:', item, '->', display);
    return display;
  }

  addComment() {
    const html = this.editorContent;
    if ((!html || !html.trim()) && this.pendingFiles.length === 0) return;

    // Get the HTML content from Quill editor
    let processedText = html;

    // Create new comment object with proper structure
    const newComment = {
      type: 'comment' as const,
      userName: this.currentUser.userName,
      userInitials: this.getAvatarText(this.currentUser.userName),

      text: processedText,
      timestamp: new Date().toISOString(),
      compact: true,
      files: this.pendingFiles.slice(),
    };

    // Add to comments array directly
    this.comments = [...this.comments, newComment];
    this.updateGroupedComments();

    // Also update the selectedTicket comments for persistence
    if (this.selectedTicket) {
      if (!this.selectedTicket.comments) {
        this.selectedTicket.comments = [];
      }
      this.selectedTicket.comments.push({
        user: this.currentUser.userName,
        text: processedText,
        date: new Date().toISOString(),
        files: this.pendingFiles.slice(),
      });
    }

    // Clear editor content, pending files, and mentions
    this.editorContent = '';
    this.pendingFiles = [];

    // Clear the Quill editor
    const mainEditor = this.quillEditors['main'];
    if (mainEditor) {
      mainEditor.setText('');
    }

    // Trigger change detection and scroll
    this.cdr.detectChanges();
    setTimeout(() => this.scrollToLastComment(), 100);
  }

  // Helper method to flatten notes from discussions
  private flattenNotesFromDiscussions(discussions: any[]): any[] {
    const allNotes: any[] = [];
    discussions.forEach(discussion => {
      if (Array.isArray(discussion.notes)) {
        discussion.notes.forEach(note => {
          // Only push if note has a valid id and body
          if (note && typeof note.id !== 'undefined' && typeof note.body !== 'undefined') {
            // Determine if this is a system activity based on content or properties
            const isSystemActivity = this.detectSystemActivity(note, discussion);

            allNotes.push({
              ...note,
              discussionId: discussion.individual_note ? undefined : discussion.id,
              individual_note: discussion.individual_note,
              type: isSystemActivity ? 'system' : 'comment',
              userName: note.author?.name || '',
              userInitials: this.getAvatarText(note.author?.name || ''),
              avatarUrl: note.author?.avatar_url || '',
              text: note.body,
              timestamp: note.created_at,
              id: note.id,
              system: note.system // Preserve the original system property
            });
          }
        });
      }
    });
    return allNotes;
  }

  // Helper method to detect if a note is a system activity
  private detectSystemActivity(note: any, discussion: any): boolean {
    // Check if it's explicitly marked as system
    if (note.system === true) {
      return true;
    }

    // Check for system activity patterns in the note body
    const body = note.body?.toLowerCase() || '';

    // Common system activity patterns
    const systemPatterns = [
      /assigned to/,
      /unassigned/,
      /changed the due date/,
      /removed the due date/,
      /added.*label/,
      /removed.*label/,
      /changed title from/,
      /closed via/,
      /reopened/,
      /closed this issue/,
      /changed milestone/,
      /removed milestone/,
      /locked this issue/,
      /unlocked this issue/,
      /moved this issue/,
      /mentioned in/
    ];

    // Check if the note body matches any system activity pattern
    const isSystemPattern = systemPatterns.some(pattern => pattern.test(body));

    // Additional check: if the note has no meaningful user content and looks automated
    const hasSystemKeywords = body.includes('changed') || body.includes('added') ||
                              body.includes('removed') || body.includes('assigned') ||
                              body.includes('closed') || body.includes('reopened');

    return isSystemPattern || (hasSystemKeywords && body.length < 200);
  }

  markTicketAsRead(ticketId: string) {
    const idx = this.tickets.findIndex(t => t.id === ticketId);
    if (idx !== -1) {
      this.tickets[idx] = { ...this.tickets[idx], read: true };
    }
  }

  // Helper to get a color for a user (for avatar background)
  getAvatarColor(name: string): string {
    // Simple hash to color
    const colors = ['#6a737d', '#2e5bff', '#43a047', '#fbc02d', '#e24329', '#1976d2', '#0366d6'];
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  }



  // Helper for avatar text (first two letters, uppercase)
  getAvatarText(user: string): string {
    return user.slice(0, 2).toUpperCase();
  }

  // Helper to check if an item is a system activity
  isSystemActivity(comment: any): boolean {
    // Check multiple ways to identify system activities
    return comment.type === 'system' ||
           comment.type === 'ticket-created' ||
           comment.system === true;
  }

  // Generate system activity for title changes
  generateSystemActivityForTitleChange(oldTitle: string, newTitle: string) {
    const currentUser = this.currentUser?.userName || 'System';
    if (oldTitle && newTitle && oldTitle !== newTitle) {
      const activityText = `changed title from <strong>${oldTitle}</strong> to <strong>${newTitle}</strong>`;
      this.addSystemActivityToTimeline(activityText, currentUser);
    }
  }

  // Generate system activity for label changes
  generateSystemActivityForLabelChange(oldLabel: string, newLabel: string) {
    const currentUser = this.currentUser?.userName || 'System';
    let activityText = '';

    if (!oldLabel && newLabel) {
      // Added label
      activityText = `added <span class="system-label">${newLabel}</span> label`;
    } else if (oldLabel && !newLabel) {
      // Removed label
      activityText = `removed <span class="system-label">${oldLabel}</span> label`;
    } else if (oldLabel && newLabel && oldLabel !== newLabel) {
      // Changed label
      activityText = `added <span class="system-label">${newLabel}</span> and removed <span class="system-label">${oldLabel}</span> labels`;
    } else {
      return; // No change
    }

    this.addSystemActivityToTimeline(activityText, currentUser);
  }

  // Generate system activity for assignee changes
  generateSystemActivityForAssigneeChange(oldAssignee: any, newAssignee: any) {
    const currentUser = this.currentUser?.userName || 'System';
    let activityText = '';

    if (!oldAssignee && newAssignee) {
      // Assigned
      activityText = `assigned to <strong>@${newAssignee.username || newAssignee.name}</strong>`;
    } else if (oldAssignee && !newAssignee) {
      // Unassigned
      activityText = `unassigned <strong>@${oldAssignee.username || oldAssignee.name}</strong>`;
    } else if (oldAssignee && newAssignee && oldAssignee.id !== newAssignee.id) {
      // Changed assignee
      activityText = `changed assignee from <strong>@${oldAssignee.username || oldAssignee.name}</strong> to <strong>@${newAssignee.username || newAssignee.name}</strong>`;
    } else {
      return; // No change
    }

    this.addSystemActivityToTimeline(activityText, currentUser);
  }

  // Generate system activity for milestone changes
  generateSystemActivityForMilestoneChange(oldMilestone: any, newMilestone: any) {
    const currentUser = this.currentUser?.userName || 'System';
    let activityText = '';

    if (!oldMilestone && newMilestone) {
      // Added milestone
      activityText = `added to <strong>${newMilestone.title}</strong> milestone`;
    } else if (oldMilestone && !newMilestone) {
      // Removed milestone
      activityText = `removed from <strong>${oldMilestone.title}</strong> milestone`;
    } else if (oldMilestone && newMilestone && oldMilestone.id !== newMilestone.id) {
      // Changed milestone
      activityText = `changed milestone from <strong>${oldMilestone.title}</strong> to <strong>${newMilestone.title}</strong>`;
    } else {
      return; // No change
    }

    this.addSystemActivityToTimeline(activityText, currentUser);
  }

  // Generate system activity for due date changes
  generateSystemActivityForDueDateChange(oldDueDate: string, newDueDate: string) {
    const currentUser = this.currentUser?.userName || 'System';
    let activityText = '';

    if (!oldDueDate && newDueDate) {
      // Added due date
      activityText = `changed due date to <strong>${new Date(newDueDate).toLocaleDateString()}</strong>`;
    } else if (oldDueDate && !newDueDate) {
      // Removed due date
      activityText = `removed due date`;
    } else if (oldDueDate && newDueDate && oldDueDate !== newDueDate) {
      // Changed due date
      activityText = `changed due date from <strong>${new Date(oldDueDate).toLocaleDateString()}</strong> to <strong>${new Date(newDueDate).toLocaleDateString()}</strong>`;
    } else {
      return; // No change
    }

    this.addSystemActivityToTimeline(activityText, currentUser);
  }

  // Generate system activity for state changes (open/close)
  generateSystemActivityForStateChange(oldState: string, newState: string) {
    const currentUser = this.currentUser?.userName || 'System';
    let activityText = '';

    if (oldState !== newState) {
      if (newState === 'closed') {
        activityText = `closed this issue`;
      } else if (newState === 'opened' || newState === 'open') {
        activityText = `reopened this issue`;
      } else {
        activityText = `changed state from <strong>${oldState}</strong> to <strong>${newState}</strong>`;
      }
      this.addSystemActivityToTimeline(activityText, currentUser);
    }
  }

  // Generate system activity for parent issue changes
  generateSystemActivityForParentChange(oldParent: any, newParent: any) {
    const currentUser = this.currentUser?.userName || 'System';
    let activityText = '';

    if (!oldParent && newParent) {
      // Added parent
      activityText = `added parent issue <strong>#${newParent.iid}</strong>`;
    } else if (oldParent && !newParent) {
      // Removed parent
      activityText = `removed parent issue <strong>#${oldParent.iid}</strong>`;
    } else if (oldParent && newParent && oldParent.id !== newParent.id) {
      // Changed parent
      activityText = `changed parent issue from <strong>#${oldParent.iid}</strong> to <strong>#${newParent.iid}</strong>`;
    } else {
      return; // No change
    }

    this.addSystemActivityToTimeline(activityText, currentUser);
  }

  // Helper method to add system activity to timeline
  addSystemActivityToTimeline(activityText: string, userName: string) {
    console.log('🔵 Adding system activity:', activityText, 'by:', userName);

    const systemActivity = {
      id: `system-${Date.now()}-${Math.random()}`,
      type: 'system',
      text: activityText,
      body: activityText,
      timestamp: new Date().toISOString(),
      userName: userName,
      user: userName,
      system: true,
      individual_note: true,
      author: {
        name: userName,
        username: userName.toLowerCase().replace(/\s+/g, ''),
        avatar_url: this.currentUser?.avatarUrl || null
      }
    };

    console.log('🔵 System activity object:', systemActivity);

    // Add to comments array and update timeline
    this.comments.push(systemActivity);
    console.log('🔵 Total comments after adding:', this.comments.length);

    this.comments.sort((a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );

    this.updateGroupedComments();
    console.log('🔵 Grouped comments after update:', this.groupedComments.length);

    this.cdr.detectChanges();
  }

  // TEST METHOD - Call this manually to test system activity generation
  testSystemActivity() {
    console.log('🧪 Testing system activity generation...');
    this.addSystemActivityToTimeline('added <span class="system-label">test</span> label', 'Test User');
  }

  // Comprehensive method to handle any ticket property updates and generate system activities
  updateTicketProperty(propertyName: string, oldValue: any, newValue: any, updatePayload: any) {
    if (!this.selectedTicket) return;

    const projectId = this.getCurrentProjectId();
    if (!this.subscriptionId || !projectId) return;

    this.isSubmitting = true;

    // Update via API
    this.discussionCommentsService.updateIssue(
      this.subscriptionId,
      projectId,
      this.selectedTicket.iid,
      updatePayload
    ).subscribe({
      next: () => {
        // Update local ticket object
        this.selectedTicket[propertyName] = newValue;
        this.selectedTicket.updated_at = new Date().toISOString();

        // Generate appropriate system activity
        switch (propertyName) {
          case 'labels':
          case 'label':
            this.generateSystemActivityForLabelChange(oldValue, newValue);
            break;
          case 'assignee_ids':
          case 'assignees':
            this.generateSystemActivityForAssigneeChange(oldValue, newValue);
            break;
          case 'milestone_id':
          case 'milestone':
            this.generateSystemActivityForMilestoneChange(oldValue, newValue);
            break;
          case 'due_date':
            this.generateSystemActivityForDueDateChange(oldValue, newValue);
            break;
          case 'state':
          case 'state_event':
            this.generateSystemActivityForStateChange(oldValue, newValue);
            break;
          case 'title':
            this.generateSystemActivityForTitleChange(oldValue, newValue);
            break;
          case 'parent_id':
          case 'parent':
            this.generateSystemActivityForParentChange(oldValue, newValue);
            break;
        }

        this.isSubmitting = false;
        this.cdr.detectChanges();
      },
      error: (err) => {
        console.error('Failed to update ticket property:', err);
        this.isSubmitting = false;
      }
    });
  }



  scrollToBottom() {
    try {
      if (this.chatList && this.chatList.nativeElement) {
        this.chatList.nativeElement.scrollTop = this.chatList.nativeElement.scrollHeight;
      }
    } catch (e) {}
  }

  sendComment() {
    if (!this.editorContent || !this.selectedTicket) {
      return;
    }
    this.isCommentSubmitting = true;

    const projectId = this.getCurrentProjectId();
    const issueIid = this.getCurrentIssueIid();
    if (!this.subscriptionId || !projectId || !issueIid) return;
    const body = this.editorContent;

    const newComment = {
      type: 'comment',
      userName: this.currentUser.userName,
      userInitials: this.getAvatarText(this.currentUser.userName),

      text: body,
      timestamp: new Date().toISOString(),
      compact: true,
      // Ensure no discussionId for standalone
      discussionId: undefined,
    };

    const onSuccess = () => {
      this.editorContent = '';
      this.comments = [...this.comments, newComment];
      this.updateGroupedComments();
      this.isCommentSubmitting = false;
      setTimeout(() => this.scrollToLastComment(), 100);
      this.fetchDiscussionAfterNote();
    };

    // Always add as a new note, not as a reply to a discussion
    this.discussionCommentsService.addNoteToIssue(this.subscriptionId, projectId, issueIid, body).subscribe({
      next: onSuccess,
      error: (err) => {
        this.isCommentSubmitting = false;
      }
    });
  }

  // Helper to fetch discussion/comments after a note is added
  fetchDiscussionAfterNote() {
    if (!this.selectedTicket) return;
    const projectId = this.getCurrentProjectId();
    const issueIid = this.getCurrentIssueIid();
    if (!this.subscriptionId || !projectId || !issueIid) return;
    this.discussionCommentsService.getDiscussions(this.subscriptionId, projectId, issueIid).subscribe({
      next: (discussions) => {
        let discussionNotes: any[] = [];
        if (discussions && discussions.length > 0) {
          this.selectedTicket.discussionId = discussions[0].id;
          discussionNotes = this.flattenNotesFromDiscussions(discussions);
        }
        // Optionally, you can add the ticket creation event if needed
        this.comments = [...discussionNotes];
        this.updateGroupedComments();
        this.cdr.detectChanges();
        setTimeout(() => {
          this.scrollToLastComment();
        }, 100);
      },
      error: (err) => {
      }
    });
  }

  scrollToLastComment() {
    if (this.lastCommentDivs && this.lastCommentDivs.length > 0) {
      const lastDiv = this.lastCommentDivs.last;
      if (lastDiv && lastDiv.nativeElement) {
        lastDiv.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    }
  }

  // Helper: Sort comments chronologically (oldest to newest)
  get sortedComments() {
    const filtered = this.comments
      .filter(c => !c.deleted && !c.is_deleted && c.state !== 'deleted')
      .slice()
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    return filtered;
  }

  // Helper: Get date label for separator
  getDateLabel(dateString: string): string {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);
    const isToday = date.toDateString() === today.toDateString();
    const isYesterday = date.toDateString() === yesterday.toDateString();
    if (isToday) return 'Today';
    if (isYesterday) return 'Yesterday';
    return formatDate(date, 'MMM d, yyyy', 'en-US');
  }

  // Helper: Should show date separator above this message?
  shouldShowDateSeparator(index: number, messages: any[]): boolean {
    if (index === 0) return false; // Don't show separator for the first message (ticket creation)
    const prev = messages[index - 1];
    const curr = messages[index];
    const prevDate = new Date(prev.timestamp).toDateString();
    const currDate = new Date(curr.timestamp).toDateString();
    return prevDate !== currDate;
  }

  // Cleanup method for mention dropdown
  ngOnDestroy() {
    for (const key in this.mentionDropdowns) {
      if (this.mentionDropdowns.hasOwnProperty(key)) {
        this.mentionDropdowns[key].remove();
        this.mentionDropdowns[key] = null;
      }
    }
    // Removed polling logic
    // this.stopCommentPolling();
  }

  // Add a comment using the new API
  addCommentViaApi() {
    const subscriptionId = '91LE9ZW';
    const projectId = '2';
    const issueIid = 4;
    const discussionId = '15c15108505fce7334f1a43f857163bf12c2d24f';
    const commentBody = 'Your comment text here';
    this.discussionCommentsService
      .addComment(subscriptionId, projectId, issueIid, discussionId, commentBody)
      .subscribe({
        next: (response) => {
        },
        error: (err) => {
        }
      });
  }

  // Update a comment using the new API
  updateCommentViaApi() {
    const subscriptionId = '91LE9ZW';
    const projectId = '2';
    const issueIid = 4;
    const discussionId = '15c15108505fce7334f1a43f857163bf12c2d24f';
    const noteId = 16; // The ID of the comment to update
    const updatedBody = 'Updated comment text';
    this.discussionCommentsService
              .updateCommentInDiscussionLegacy(subscriptionId, projectId, issueIid, discussionId, noteId, updatedBody)
      .subscribe({
        next: (response) => {
        },
        error: (err) => {
        }
      });
  }

  // Delete a comment using the new API
  deleteCommentViaApi() {
    const subscriptionId = '91LE9ZW';
    const projectId = '2';
    const issueIid = 4;
    const discussionId = '15c15108505fce7334f1a43f857163bf12c2d24f';
    const noteId = 16; // The ID of the comment to delete
    this.discussionCommentsService
              .deleteCommentInDiscussionLegacy(subscriptionId, projectId, issueIid, discussionId, noteId)
      .subscribe({
        next: () => {
        },
        error: (err) => {
        }
      });
  }

  getDisplayUserName(item: any): string {
    // Try to match by username, user, or email
    const current = this.currentUser.userName?.toLowerCase() || '';
    const currentEmail = this.currentUser.email?.toLowerCase() || '';
    const itemUser = (item.userName || item.user || '').toLowerCase();
    const itemEmail = (item.email || '').toLowerCase();

    if (
      itemUser === current ||
      itemUser === currentEmail ||
      itemEmail === current ||
      itemEmail === currentEmail
    ) {
      return this.currentUser.userName;
    }
    // Fallback: show part before @ if it's an email
    if (itemUser.includes('@')) {
      return itemUser.split('@')[0];
    }
    return item.userName || item.user || 'Unknown';
  }

  // Start editing a comment
    startEditComment(item: any) {
    // If already editing another comment, cancel it first
    if (this.editingCommentId && this.editingCommentId !== item.id) {
      this.cancelEditComment();
    }

    this.editingCommentId = item.id;
    // Use the appropriate content field (text or body)
    this.editingCommentContent = item.text || item.body || '';

    // Focus the editor after a short delay to ensure it's rendered
    setTimeout(() => {
      const editEditor = this.quillEditors['edit-' + item.id];
      if (editEditor) {
        editEditor.focus();
      }
    }, 100);
    setTimeout(() => this.calculateParentOffsets(), 0);
  }

  // Cancel editing
  cancelEditComment() {
    this.editingCommentId = null;
    this.editingCommentContent = '';

    // Clear any existing mention dropdown
    this.hideMentionDropdown('main'); // Assuming 'main' is the context for the main editor
    setTimeout(() => this.calculateParentOffsets(), 0);
  }

  // Save edited comment
  saveEditedComment(item: any) {
    if (!item.id) {
      return;
    }
    if (!this.selectedTicket || !this.editingCommentContent.trim()) return;

    this.isCommentSubmitting = true;
    const projectId = this.getCurrentProjectId();
    const issueIid = this.getCurrentIssueIid();
    const noteId = item.id;
    const updatedBody = this.editingCommentContent;

    if (item.discussionId) {
      // Discussion note
      const discussionId = item.discussionId;
      this.discussionCommentsService.updateCommentInDiscussionLegacy(this.subscriptionId, projectId, issueIid, discussionId, noteId, updatedBody)
        .subscribe({
          next: () => {
            // Update both body and text fields to ensure consistency
            const idx = this.comments.findIndex((c: any) => c.id === item.id);
            if (idx !== -1) {
              this.comments[idx].body = updatedBody;
              this.comments[idx].text = updatedBody;
            }
            this.updateGroupedComments();
            this.isCommentSubmitting = false;
            this.cancelEditComment();
            this.cdr.detectChanges();
          },
          error: (err) => {
            this.isCommentSubmitting = false;
          }
        });
    } else {
      // Top-level note
      this.discussionCommentsService.updateNoteOnIssue(this.subscriptionId, projectId, issueIid, noteId, updatedBody)
        .subscribe({
          next: () => {
            // Update both body and text fields to ensure consistency
            const idx = this.comments.findIndex((c: any) => c.id === item.id);
            if (idx !== -1) {
              this.comments[idx].body = updatedBody;
              this.comments[idx].text = updatedBody;
            }
            this.updateGroupedComments();
            this.isCommentSubmitting = false;
            this.cancelEditComment();
            this.cdr.detectChanges();
          },
          error: (err) => {
            this.isCommentSubmitting = false;
          }
        });
    }
  }

  // Delete a comment
  deleteComment(item: any) {
    if (!item.id) {
      return;
    }
    if (!this.selectedTicket) return;
    if (!confirm('Are you sure you want to delete this comment?')) return;
    this.isCommentSubmitting = true;
    const projectId = this.getCurrentProjectId();
    const issueIid = this.getCurrentIssueIid();
    const noteId = item.id;
    if (item.discussionId) {
      // Discussion note
      const discussionId = item.discussionId;
      this.discussionCommentsService.deleteCommentInDiscussionLegacy(this.subscriptionId, projectId, issueIid, discussionId, noteId)
        .subscribe({
          next: () => {
            this.comments = this.comments.filter((c: any) => c.id !== item.id);
            this.updateGroupedComments();
            this.isCommentSubmitting = false;
            this.cancelEditComment();
          },
          error: (err) => {
            this.isCommentSubmitting = false;
          }
        });
    } else {
      // Top-level note
      this.discussionCommentsService.deleteNoteOnIssue(this.subscriptionId, projectId, issueIid, noteId)
        .subscribe({
          next: () => {
            this.comments = this.comments.filter((c: any) => c.id !== item.id);
            this.updateGroupedComments();
            this.isCommentSubmitting = false;
            this.cancelEditComment();
          },
          error: (err) => {
            this.isCommentSubmitting = false;
          }
        });
    }
  }

    // Start replying to a comment
  startReply(comment: any) {
    this.replyingToCommentId = comment.id;
    this.replyContent[comment.id] = '';
    setTimeout(() => this.calculateParentOffsets(), 0);
  }

  // Start replying to the main ticket
  startReplyToTicket() {
    this.replyingToCommentId = 'ticket';
    this.replyContent['ticket'] = '';
  }

  // Cancel replying
  cancelReply() {
    if (this.replyingToCommentId) {
      delete this.replyContent[this.replyingToCommentId];
    }
    this.replyingToCommentId = null;
    setTimeout(() => this.calculateParentOffsets(), 0);
  }

  // Send a reply to a specific comment
  sendReply(parentComment: any) {
    const replyText = this.replyContent[parentComment.id];
    if (!replyText || !replyText.trim() || !this.selectedTicket) return;

    this.isCommentSubmitting = true;
    const projectId = this.getCurrentProjectId();
    const issueIid = this.getCurrentIssueIid();
    const discussionId = parentComment.discussionId || parentComment.id;
    const replyBody = replyText;

    this.discussionCommentsService.addComment(this.subscriptionId, projectId, issueIid, discussionId, replyBody)
      .subscribe({
        next: (response) => {
          this.replyContent[parentComment.id] = '';
          this.fetchDiscussionAfterNote();
          this.isCommentSubmitting = false;
          this.cancelReply();
        },
        error: (err) => {
          this.isCommentSubmitting = false;
        }
      });
  }

  // Get unique key for a comment group
  getGroupKey(group: { isDiscussion: boolean; comments: any[] }): string {
    let groupKey: string;
    if (group.isDiscussion && group.comments.length > 0) {
      groupKey = `discussion-${group.comments[0].discussionId}`;
    } else if (group.comments.length > 0) {
      groupKey = `comment-${group.comments[0].id}`;
    } else {
      groupKey = `group-${Math.random()}`;
    }



    return groupKey;
  }

  // Check if a group is collapsed
  isGroupCollapsed(group: { isDiscussion: boolean; comments: any[] }): boolean {
    const groupKey = this.getGroupKey(group);
    const isCollapsed = this.collapsedGroups.has(groupKey);
    return isCollapsed;
  }

  // Toggle collapse/expand for a group
  toggleGroupCollapse(group: { isDiscussion: boolean; comments: any[] }) {
    const groupKey = this.getGroupKey(group);
    if (this.collapsedGroups.has(groupKey)) {
      this.collapsedGroups.delete(groupKey);
    } else {
      this.collapsedGroups.add(groupKey);
    }
    this.cdr.detectChanges();
    setTimeout(() => this.calculateParentOffsets(), 0); // Ensure timeline updates after DOM changes
  }

  // Get total number of groups that can be collapsed
  getCollapsibleGroupsCount(): number {
    return this.groupedComments.filter(group =>
      group.isDiscussion && group.comments.length > 1
    ).length;
  }

  // Update grouped comments - GitHub style flat timeline
  updateGroupedComments() {
    // Create a flat timeline where each item is individual (GitHub style)
    const timelineItems: { isDiscussion: boolean; comments: any[]; timelineType: 'comment' | 'system' | 'discussion' }[] = [];

    // Process all comments chronologically
    for (const comment of this.sortedComments) {
      console.log('🔍 Processing comment:', comment.id, 'type:', comment.type, 'system:', comment.system);

      if (comment.type === 'system' || comment.type === 'ticket-created' || comment.system === true) {
        // System activities - show as individual items with dots
        console.log('✅ Adding system activity to timeline:', comment.text || comment.body);
        timelineItems.push({
          isDiscussion: false,
          comments: [comment],
          timelineType: 'system'
        });
      } else if (comment.discussionId && !comment.individual_note) {
        // For discussion threads, we need to group them but still show in timeline
        const existingDiscussion = timelineItems.find(item =>
          item.timelineType === 'discussion' &&
          item.comments[0]?.discussionId === comment.discussionId
        );

        if (existingDiscussion) {
          // Add to existing discussion
          existingDiscussion.comments.push(comment);
        } else {
          // Create new discussion group
          timelineItems.push({
            isDiscussion: true,
            comments: [comment],
            timelineType: 'discussion'
          });
        }
      } else {
        // Individual comments - show as individual items with avatars
        timelineItems.push({
          isDiscussion: false,
          comments: [comment],
          timelineType: 'comment'
        });
      }
    }

    // Sort timeline items by the timestamp of their first comment
    timelineItems.sort((a, b) => {
      const aTime = new Date(a.comments[0]?.timestamp || 0).getTime();
      const bTime = new Date(b.comments[0]?.timestamp || 0).getTime();
      return aTime - bTime;
    });

    // If no comments exist, add some sample data for demonstration
    if (timelineItems.length === 0 && this.comments.length === 0) {
      timelineItems.push(...this.getSampleGitHubStyleTimeline());
    }

    this.groupedComments = timelineItems;
  }

  // Sample timeline showing how real API data will be displayed
  getSampleGitHubStyleTimeline(): { isDiscussion: boolean; comments: any[]; timelineType: 'comment' | 'system' | 'discussion' }[] {
    // This shows how your real API data will appear in the timeline
    // Based on your actual API structure with system activities
    return [
      // Real system activity from your API (system: true)
      {
        isDiscussion: false,
        timelineType: 'system',
        comments: [{
          id: 24,
          type: 'system',
          text: 'changed start date to July 15, 2025 and changed due date to July 19, 2025',
          timestamp: '2025-07-03T23:56:16.339Z',
          userName: 'cax-development',
          user: 'cax-development',
          avatarUrl: 'https://www.gravatar.com/avatar/ca20e5a9dbf493c290d1e16b2c97d117868fedcb5cce4495ec989d76f1a903b4?s=80&d=identicon',
          system: true,
          individual_note: true
        }]
      },
      // Real user comment from your API (system: false)
      {
        isDiscussion: false,
        timelineType: 'comment',
        comments: [{
          id: 3,
          type: 'comment',
          text: '<p><span style="color: rgb(9, 105, 218);">@cax-development </span> fsadfdsafsdfds dsafdsfsafds</p>',
          timestamp: '2025-07-03T08:23:03.186Z',
          userName: 'sunilc | Dev',
          user: 'sunilc',
          avatarUrl: 'https://www.gravatar.com/avatar/e56b706f712a4660e9cf817708385653fa66c14163f52cc58a02b09374d52615?s=80&d=identicon',
          system: false,
          individual_note: true
        }]
      },
      // Real discussion thread from your API (individual_note: false)
      {
        isDiscussion: true,
        timelineType: 'discussion',
        comments: [
          {
            id: 6,
            type: 'DiscussionNote',
            text: '<p>sandDKJ</p>',
            timestamp: '2025-07-03T09:05:01.255Z',
            userName: 'sunilc | Dev',
            user: 'sunilc',
            avatarUrl: 'https://www.gravatar.com/avatar/e56b706f712a4660e9cf817708385653fa66c14163f52cc58a02b09374d52615?s=80&d=identicon',
            system: false,
            individual_note: false
          },
          {
            id: 9,
            type: 'DiscussionNote',
            text: 'fdsafdsafds',
            timestamp: '2025-07-03T10:07:21.238Z',
            userName: 'sunilc | Dev',
            user: 'sunilc',
            avatarUrl: 'https://www.gravatar.com/avatar/e56b706f712a4660e9cf817708385653fa66c14163f52cc58a02b09374d52615?s=80&d=identicon',
            system: false,
            individual_note: false
          }
        ]
      }
    ];
  }

  get lastAvatarCenter(): number {
    if (!this.parentOffsets.length) return 0;
    // 36 is the avatar height (px)
    return this.parentOffsets[this.parentOffsets.length - 1] + 18;
  }

  // Helper to get the current projectId dynamically
  getCurrentProjectId(): string | null {
    return this.selectedSupplier?.git_project_id || this.currentProjectId || null;
  }

  // Helper to get the current groupId dynamically
  getCurrentGroupId(): string | null {
    return this.currentGroupId || null;
  }

  // Helper to get the current issue iid dynamically
  getCurrentIssueIid(): number | null {
    return this.selectedTicket?.iid || null;
  }

  get isLabelChanged(): boolean {
    return this.sidebarMode === 'edit' && this.selectedLabel !== this.originalLabel;
  }

  // Add this method to handle label change and save immediately
  onLabelChange() {
    if (this.sidebarMode === 'edit' && this.selectedTicket && this.selectedLabel !== this.originalLabel) {
      this.isSubmitting = true;
      const projectId = this.getCurrentProjectId();
      if (!this.subscriptionId || !projectId) return;

      // Store old and new values for system activity
      const oldLabel = this.originalLabel;
      const newLabel = this.selectedLabel;

      // PATCH to correct API endpoint for updating label
      this.discussionCommentsService.updateIssue(
        this.subscriptionId,
        projectId,
        this.selectedTicket.iid,
        { labels: this.selectedLabel || '' }
      ).subscribe({
        next: () => {
          this.selectedTicket.label = this.selectedLabel;
          this.selectedTicket.updated_at = new Date().toISOString();
          this.originalLabel = this.selectedLabel;

          // Generate system activity for label change
          this.generateSystemActivityForLabelChange(oldLabel, newLabel);

          this.isSubmitting = false;
          this.cdr.detectChanges();
          this.loadTickets(); // Refresh the ticket list
        },
        error: () => {
          this.isSubmitting = false;
        }
      });
    }
  }

  // Handler for clicking a file attachment in a comment
  async onAttachmentClick(file: { name: string; file_path: string; type: string }) {
    if (!this.subscriptionId || !file.file_path) return;
    try {
      const resp = await this.discussionCommentsService.getAttachmentDownloadSignedUrl(this.subscriptionId, file.file_path).toPromise();
      const url = resp.signed_url;
      if (this.isViewableFileType(file.type, file.name)) {
        window.open(url, '_blank');
      } else {
        const a = document.createElement('a');
        a.href = url;
        a.download = file.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    } catch (err) {
      alert('Failed to download file.');
    }
  }

  // Helper to check if a file is viewable in browser
  isViewableFileType(type: string, name: string): boolean {
    const viewableTypes = [
      'image/png', 'image/jpeg', 'image/gif', 'image/bmp', 'image/svg+xml',
      'application/pdf',
    ];
    const ext = name.split('.').pop()?.toLowerCase();
    return (
      viewableTypes.includes(type) ||
      ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg', 'pdf'].includes(ext || '')
    );
  }
}


