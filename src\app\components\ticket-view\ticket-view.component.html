<div class="ticket-view-container">
  <div class="ticket-header-row">
    <div class="ticket-header-title">
      <span>Help-Center</span>
    </div>
    <button mat-raised-button color="primary" (click)="onRaiseTicket()">Raise Ticket</button>
  </div>

  <div class="ticket-tabs-row">
    <div
      *ngFor="let tab of tabs"
      class="ticket-tab"
      [class.active]="selectedTab.id === tab.id"
      (click)="selectTab(tab)"
    >
      {{ tab.header }}
      <span *ngIf="tab.id === 'all' && allTicketsCount > 0" class="tab-badge">{{ allTicketsCount }}</span>
      <span *ngIf="tab.id === 'my-tickets' && myTicketsCount > 0" class="tab-badge">{{ myTicketsCount }}</span>
    </div>
  </div>

  <div class="ticket-list">
    <!-- Loader: Centered -->
    <div *ngIf="ticketListLoading" class="sidebar-loading-spinner ticket-list-loader">
      <mat-spinner diameter="32"></mat-spinner>
    </div>

    <!-- No Data Message: Only after first load -->
    <div *ngIf="!ticketListLoading && filteredTickets.length === 0 && ticketListLoadedOnce" class="no-data-message">
      No tickets found.
    </div>

    <!-- Ticket List -->
    <ng-container *ngIf="!ticketListLoading && filteredTickets.length > 0">
      <div
        *ngFor="let ticket of filteredTickets"
        class="ticket-item"
        [class.selected]="selectedTicketId === ticket.id"
        [class.unread]="!ticket.read"
        (click)="onTicketClick(ticket)"
      >
        <div class="ticket-item-main">
          <div class="ticket-item-type-row">
            <span class="ticket-item-type">{{ ticket.title }}</span>
            <span *ngIf="ticket.label" class="ticket-item-badge label-badge">{{ ticket.label }}</span>
            <span class="ticket-item-badge status-badge" [ngClass]="ticket.status">{{ ticket.status | titlecase }}</span>
          </div>
          <div class="ticket-item-message">{{ ticket.message }}</div>
        </div>
        <div class="ticket-item-meta">
          <span class="ticket-item-date">{{ ticket.date | date: 'MMM d' }}</span>
          <span class="ticket-item-user"><span class="by">-- by </span>{{ logDisplayAuthor(ticket) }}</span>
        </div>
      </div>
    </ng-container>
  </div>
</div>

<!-- Sidebar backdrop -->
<div *ngIf="sidebarOpen" class="sidebar-backdrop" [ngClass]="{'sidebar-open': sidebarOpen}" (click)="onCancel()"></div>

<div class="ticket-sidebar" *ngIf="sidebarOpen" [ngClass]="{'sidebar-open': sidebarOpen}">
  <!-- Header: Always visible -->
  <div class="sidebar-header">
    <div class="header-content">
      <h2 class="header-title">
        <span *ngIf="sidebarMode === 'edit'">Edit Ticket</span>
        <span *ngIf="sidebarMode === 'new'">Raise Ticket</span>
      </h2>
      <button class="header-close-btn" (click)="onCancel()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- Main Content Area: Show spinner or hide while loading -->
  <div class="sidebar-content">
    <!-- Show group labels as chips -->
    <!-- <div *ngIf="groupLabels.length" class="group-labels-row" style="margin-bottom: 12px; display: flex; flex-wrap: wrap; gap: 8px;">
      <span *ngFor="let label of groupLabels" class="label-chip" [ngStyle]="{'background': label.color, 'color': label.text_color, 'padding': '4px 12px', 'border-radius': '12px', 'font-size': '13px', 'font-weight': 500}">
        {{ label.name }}
      </span>
    </div> -->
    <div *ngIf="isSubmitting || commentSectionLoading" class="sidebar-loading-spinner" style="display: flex; justify-content: center; align-items: center; min-height: 200px;">
      <mat-spinner diameter="48"></mat-spinner>
    </div>
    <ng-container *ngIf="!isSubmitting && !commentSectionLoading">
      <!-- Ticket Info Row (vertical, key-value, key bold/dark, value lighter) -->
      <div *ngIf="sidebarMode === 'edit' && selectedTicket" class="ticket-info-vertical">
        <div class="info-line"><span class="info-label">Ticket ID:</span> <span class="info-value">{{ selectedTicket.ticketId }}</span></div>
        <div class="info-line"><span class="info-label">Created by:</span> <span class="info-value">{{ selectedTicket.user }}</span></div>
        <div class="info-line"><span class="info-label">Date:</span> <span class="info-value">{{ selectedTicket.date | date: 'MMM d, y' }}</span></div>
        <div class="info-line">
          <span class="info-label">Status:</span>
          <span class="info-value status-badge" [ngClass]="selectedTicket.status">
            {{ selectedTicket.status | titlecase }}
          </span>
        </div>
      </div>
      <!-- Form Fields Section -->
      <div class="form-section">
        <div class="form-field-group">
          <label class="form-label">Title <span class="required">*</span></label>
          <input
            class="form-input"
            [class.error]="formErrors.title"
            type="text"
            [(ngModel)]="editTitle"
            (input)="onFieldChange('title')"
            [disabled]="sidebarMode === 'edit' && !isTicketOwner"
            placeholder="Enter ticket title" />
          <div *ngIf="formErrors.title" class="error-message">{{ formErrors.title }}</div>
        </div>

        <!-- Label Dropdown -->
        <div class="form-field-group">
          <label class="form-label">Label</label>
          <select
            class="form-input"
            [(ngModel)]="selectedLabel"
            (change)="onLabelChange()">
            <option value="">Select label (optional)</option>
            <ng-container *ngFor="let label of (projectLabels.length ? projectLabels : groupLabels)">
              <option [value]="label.name">{{ label.name }}</option>
            </ng-container>
          </select>
        </div>

        <div class="form-field-group">
          <label class="form-label">Description</label>
          <textarea
            class="form-textarea"
            [class.error]="formErrors.message"
            rows="3"
            [(ngModel)]="editMessage"
            (input)="onFieldChange('message')"
            [disabled]="sidebarMode === 'edit' && !isTicketOwner"
            placeholder="Describe your issue or request (optional)"></textarea>
          <div *ngIf="formErrors.message" class="error-message">{{ formErrors.message }}</div>
        </div>
      </div>
      <!-- Timeline Section -->
      <div class="timeline-section">
        <div class="timeline-header">
          <h3 class="timeline-title">Activity</h3>
          <!-- PROMINENT TEST BUTTON -->
          <button mat-raised-button color="warn" (click)="testSystemActivity()" style="margin-left: 16px; font-size: 12px; background: red; color: white;">
            🧪 TEST SYSTEM ACTIVITY
          </button>
        </div>

        <!-- GitHub-Style Timeline -->
        <div class="github-timeline-container">
          <ng-container *ngFor="let timelineItem of groupedComments; let i = index">

            <!-- Timeline Item Container -->
            <div class="timeline-item" [ngClass]="timelineItem.timelineType">

              <!-- Timeline Marker (Dot or Avatar) -->
              <div class="timeline-marker">
                <!-- System Activity Dot -->
                <div *ngIf="isSystemActivity(timelineItem.comments[0])" class="timeline-dot"></div>

                <!-- User Avatar -->
                <div *ngIf="!isSystemActivity(timelineItem.comments[0])" class="timeline-avatar">
                  <ng-container *ngIf="timelineItem.comments[0].avatarUrl; else avatarInitials">
                    <img [src]="timelineItem.comments[0].avatarUrl" alt="Avatar" />
                  </ng-container>
                  <ng-template #avatarInitials>
                    <span>{{ getAvatarText(timelineItem.comments[0].userName || timelineItem.comments[0].user) }}</span>
                  </ng-template>
                </div>
              </div>

              <!-- Timeline Content -->
              <div class="timeline-content">

                <!-- System Activity -->
                <div *ngIf="isSystemActivity(timelineItem.comments[0])" class="system-activity">
                  <div class="system-activity-header">
                    <span class="system-activity-text" [innerHTML]="(timelineItem.comments[0].text || timelineItem.comments[0].body) | safe:'html'"></span>
                    <span class="system-activity-timestamp">{{ timelineItem.comments[0].timestamp | timeAgo }}</span>
                  </div>
                </div>

                <!-- Regular Comment -->
                <div *ngIf="!isSystemActivity(timelineItem.comments[0]) && !timelineItem.isDiscussion" class="comment-item">
                  <div class="comment-header">
                    <span class="comment-author">{{ logDisplayAuthor(timelineItem.comments[0]) }}</span>
                    <span class="comment-timestamp">{{ timelineItem.comments[0].timestamp | timeAgo }}</span>
                    <div class="comment-actions">
                      <button mat-icon-button (click)="startEditComment(timelineItem.comments[0])" *ngIf="editingCommentId !== timelineItem.comments[0].id && canEditComment(timelineItem.comments[0])">
                        <mat-icon>edit</mat-icon>
                      </button>
                      <button mat-icon-button (click)="deleteComment(timelineItem.comments[0])" *ngIf="canDeleteComment(timelineItem.comments[0])">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                  </div>

                  <div class="comment-body" *ngIf="editingCommentId !== timelineItem.comments[0].id">
                    <div [innerHTML]="(timelineItem.comments[0].text || timelineItem.comments[0].body) | safe:'html'"></div>
                    <div *ngIf="timelineItem.comments[0].files && timelineItem.comments[0].files.length" class="comment-attachments">
                      <div *ngFor="let file of timelineItem.comments[0].files" class="comment-attachment-item">
                        <button mat-button (click)="onAttachmentClick(file)" style="text-transform:none;display:flex;align-items:center;gap:6px;">
                          <mat-icon *ngIf="isImage(file.name)">image</mat-icon>
                          <mat-icon *ngIf="!isImage(file.name)">insert_drive_file</mat-icon>
                          <span>{{ file.name }}</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Edit Mode -->
                  <div class="comment-edit" *ngIf="editingCommentId === timelineItem.comments[0].id">
                    <quill-editor
                      [(ngModel)]="editingCommentContent"
                      [modules]="quillModules"
                      placeholder="Edit your comment..."
                      class="modern-quill-editor edit-mode"
                      (onEditorCreated)="onEditorCreated($event, 'edit-' + timelineItem.comments[0].id)">
                    </quill-editor>
                    <div class="edit-actions">
                      <button mat-button color="primary" (click)="saveEditedComment(timelineItem.comments[0])" [disabled]="!editingCommentContent.trim() || isCommentSubmitting">
                        <mat-icon>save</mat-icon>
                        Save
                      </button>
                      <button mat-button (click)="cancelEditComment()" [disabled]="isCommentSubmitting">
                        <mat-icon>cancel</mat-icon>
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Discussion Thread -->
                <div *ngIf="timelineItem.isDiscussion" class="discussion-thread">
                  <!-- Parent Comment Header -->
                  <div class="thread-header-row">
                    <div class="thread-avatar">
                      <div class="avatar-circle" [ngStyle]="{'background': getAvatarColor(timelineItem.comments[0].userName || timelineItem.comments[0].user)}">
                        <ng-container *ngIf="timelineItem.comments[0].avatarUrl; else parentInitials">
                          <img [src]="timelineItem.comments[0].avatarUrl" alt="Avatar" />
                        </ng-container>
                        <ng-template #parentInitials>
                          <span>{{ getAvatarText(timelineItem.comments[0].userName || timelineItem.comments[0].user) }}</span>
                        </ng-template>
                      </div>
                    </div>
                    <div class="thread-header-main">
                      <span class="thread-username">{{ logDisplayAuthor(timelineItem.comments[0]) }}</span>
                      <span class="thread-timestamp">{{ timelineItem.comments[0].timestamp | timeAgo }}</span>
                    </div>
                    <div class="thread-header-actions" style="margin-left:auto;">
                      <button mat-icon-button (click)="startReply(timelineItem.comments[0])"><mat-icon>reply</mat-icon></button>
                      <button mat-icon-button (click)="startEditComment(timelineItem.comments[0])" *ngIf="editingCommentId !== timelineItem.comments[0].id && canEditComment(timelineItem.comments[0])"><mat-icon>edit</mat-icon></button>
                      <button mat-icon-button (click)="deleteComment(timelineItem.comments[0])" *ngIf="canDeleteComment(timelineItem.comments[0])"><mat-icon>delete</mat-icon></button>
                    </div>
                  </div>

                  <!-- Parent Comment Body -->
                  <div class="thread-body" *ngIf="editingCommentId !== timelineItem.comments[0].id">
                    <div [innerHTML]="(timelineItem.comments[0].text || timelineItem.comments[0].body) | safe:'html'"></div>
                    <div *ngIf="timelineItem.comments[0].files && timelineItem.comments[0].files.length" class="comment-attachments">
                      <div *ngFor="let file of timelineItem.comments[0].files" class="comment-attachment-item">
                        <button mat-button (click)="onAttachmentClick(file)" style="text-transform:none;display:flex;align-items:center;gap:6px;">
                          <mat-icon *ngIf="isImage(file.name)">image</mat-icon>
                          <mat-icon *ngIf="!isImage(file.name)">insert_drive_file</mat-icon>
                          <span>{{ file.name }}</span>
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Edit Mode for Parent Comment -->
                  <div class="thread-body" *ngIf="editingCommentId === timelineItem.comments[0].id">
                    <div class="comment-edit-container">
                      <quill-editor
                        [(ngModel)]="editingCommentContent"
                        [modules]="quillModules"
                        placeholder="Edit your comment..."
                        class="modern-quill-editor edit-mode"
                        (onEditorCreated)="onEditorCreated($event, 'edit-' + timelineItem.comments[0].id)">
                      </quill-editor>
                      <div class="edit-actions">
                        <button mat-button color="primary" (click)="saveEditedComment(timelineItem.comments[0])" [disabled]="!editingCommentContent.trim() || isCommentSubmitting" [class.loading]="isCommentSubmitting">
                          <mat-icon>save</mat-icon>
                          {{ isCommentSubmitting ? 'Saving...' : 'Save' }}
                        </button>
                        <button mat-button (click)="cancelEditComment()" [disabled]="isCommentSubmitting">
                          <mat-icon>cancel</mat-icon>
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Collapse/Expand Button - Show only for discussions with multiple comments -->
                  <div *ngIf="timelineItem.isDiscussion && timelineItem.comments.length > 1" class="collapse-replies-row">
                    <span class="collapse-replies-btn" (click)="toggleGroupCollapse(timelineItem)">
                      <mat-icon>{{ isGroupCollapsed(timelineItem) ? 'expand_more' : 'expand_less' }}</mat-icon>
                      {{ isGroupCollapsed(timelineItem) ? 'Expand replies' : 'Collapse replies' }}
                    </span>
                  </div>

                  <!-- Replies Section - Show only when not collapsed -->
                  <div class="thread-replies" *ngIf="timelineItem.isDiscussion && timelineItem.comments.length > 1 && !isGroupCollapsed(timelineItem)">
                    <ng-container *ngFor="let reply of timelineItem.comments.slice(1); let replyIndex = index">
                      <div class="thread-reply-row">
                        <div class="thread-avatar small">
                          <div class="avatar-circle" [ngStyle]="{'background': getAvatarColor(reply.userName || reply.user)}">
                            <ng-container *ngIf="reply.avatarUrl; else replyInitials">
                              <img [src]="reply.avatarUrl" alt="Avatar" />
                            </ng-container>
                            <ng-template #replyInitials>
                              <span>{{ getAvatarText(reply.userName || reply.user) }}</span>
                            </ng-template>
                          </div>
                        </div>
                        <div class="thread-reply-main">
                          <div class="thread-reply-header">
                            <span class="thread-username">{{ logDisplayAuthor(reply) }}</span>
                            <span class="thread-timestamp">{{ reply.timestamp | timeAgo }}</span>
                            <span class="thread-reply-actions" style="margin-left:auto;">
                              <button mat-icon-button (click)="startEditComment(reply)" *ngIf="editingCommentId !== reply.id && canEditComment(reply)"><mat-icon>edit</mat-icon></button>
                              <button mat-icon-button (click)="deleteComment(reply)" *ngIf="canDeleteComment(reply)"><mat-icon>delete</mat-icon></button>
                            </span>
                          </div>
                          <div class="thread-reply-body" *ngIf="editingCommentId !== reply.id">
                            <div [innerHTML]="(reply.text || reply.body) | safe:'html'"></div>
                            <div *ngIf="reply.files && reply.files.length" class="comment-attachments">
                              <div *ngFor="let file of reply.files" class="comment-attachment-item">
                                <button mat-button (click)="onAttachmentClick(file)" style="text-transform:none;display:flex;align-items:center;gap:6px;">
                                  <mat-icon *ngIf="isImage(file.name)">image</mat-icon>
                                  <mat-icon *ngIf="!isImage(file.name)">insert_drive_file</mat-icon>
                                  <span>{{ file.name }}</span>
                                </button>
                              </div>
                            </div>
                          </div>

                          <!-- Edit Mode for Reply Comment -->
                          <div class="thread-reply-body" *ngIf="editingCommentId === reply.id">
                            <div class="comment-edit-container">
                              <quill-editor
                                [(ngModel)]="editingCommentContent"
                                [modules]="quillModules"
                                placeholder="Edit your reply..."
                                class="modern-quill-editor edit-mode"
                                (onEditorCreated)="onEditorCreated($event, 'edit-' + reply.id)">
                              </quill-editor>
                              <div class="edit-actions">
                                <button mat-button color="primary" (click)="saveEditedComment(reply)" [disabled]="!editingCommentContent.trim() || isCommentSubmitting" [class.loading]="isCommentSubmitting">
                                  <mat-icon>save</mat-icon>
                                  {{ isCommentSubmitting ? 'Saving...' : 'Save' }}
                                </button>
                                <button mat-button (click)="cancelEditComment()" [disabled]="isCommentSubmitting">
                                  <mat-icon>cancel</mat-icon>
                                  Cancel
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </ng-container>
                  </div>

                  <!-- Reply Input & Resolve Thread -->
                  <div class="thread-footer-row" *ngIf="replyingToCommentId === timelineItem.comments[0].id">
                    <quill-editor
                      [(ngModel)]="replyContent[timelineItem.comments[0].id]"
                      [modules]="quillModules"
                      placeholder="Write a reply..."
                      class="modern-quill-editor reply-mode"
                      (onEditorCreated)="onEditorCreated($event, 'reply-' + timelineItem.comments[0].id)">
                    </quill-editor>
                    <div class="edit-actions">
                      <button mat-stroked-button color="primary" (click)="sendReply(timelineItem.comments[0])" [disabled]="!replyContent[timelineItem.comments[0].id] || isCommentSubmitting">
                        <mat-icon>save</mat-icon>
                        Reply
                      </button>
                      <button mat-stroked-button color="basic" (click)="cancelReply()" [disabled]="isCommentSubmitting">
                        <mat-icon>cancel</mat-icon>
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>

              </div> <!-- End timeline-content -->
            </div> <!-- End timeline-item -->
          </ng-container>
        </div> <!-- End github-timeline-container -->
      </div>
    </ng-container>
  </div>


  <!-- Comment Input Section: Always visible -->
  <div class="comment-input-section">
    <div class="comment-input-container" [class.disabled]="isCommentSubmitting || !isCommentEditorEnabled">
      <div *ngIf="isCommentSubmitting" class="comment-loading-spinner" style="display: flex; justify-content: center; align-items: center; min-height: 40px;">
        <mat-spinner diameter="24"></mat-spinner>
      </div>
      <quill-editor
        [(ngModel)]="editorContent"
        [modules]="quillModules"
        (onEditorCreated)="onEditorCreated($event, 'main')"
        [placeholder]="sidebarMode === 'new' && !isCommentEditorEnabled ? 'Please fill in the title first...' : 'Leave a comment... Use @ to mention users...'"
        [disabled]="!isCommentEditorEnabled || isCommentSubmitting"
        class="modern-quill-editor">
      </quill-editor>

      <div class="pending-file-list" *ngIf="pendingFiles.length">
        <div class="pending-file-item" *ngFor="let file of pendingFiles; let i = index">
          <mat-icon *ngIf="isImage(file.name)">image</mat-icon>
          <mat-icon *ngIf="!isImage(file.name)">insert_drive_file</mat-icon>
          <span class="pending-file-name">{{ file.name }}</span>
          <mat-progress-spinner *ngIf="file.uploading" diameter="18" mode="indeterminate" style="margin-left:8px;"></mat-progress-spinner>
          <span *ngIf="file.error" style="color:#d32f2f; margin-left:8px; font-size:12px;">{{ file.error }}</span>
          <button mat-icon-button color="warn" (click)="pendingFiles.splice(i, 1)">
            <mat-icon>close</mat-icon>
          </button>
        </div>
      </div>
      <div class="comment-actions" style="position:absolute; bottom:12px; right:12px;">
        <button
          class="comment-send-btn"
          [disabled]="!editorContent || !isCommentEditorEnabled || isCommentSubmitting"
          [class.disabled]="!editorContent || !isCommentEditorEnabled || isCommentSubmitting"
          (click)="sendComment()">
          <mat-icon>send</mat-icon>
        </button>
        <!-- TEST BUTTON - Remove this after testing -->
        <button mat-raised-button color="warn" (click)="testSystemActivity()" title="Test System Activity" style="margin-left: 8px; background: red; color: white;">
          🧪 TEST
        </button>
      </div>
    </div>
  </div>

  <!-- Footer: Always visible -->
  <div class="sidebar-footer">
    <button class="btn-secondary" (click)="onCancel()" [disabled]="isSubmitting">Cancel</button>
    <button
      class="btn-primary"
      (click)="saveTicket()"
      [disabled]="(sidebarMode === 'edit' && !isLabelChanged && !isTicketOwner) || isSubmitting">
      <mat-icon *ngIf="isSubmitting" class="spinner">hourglass_empty</mat-icon>
      {{ isSubmitting ? 'Saving...' : 'Save Changes' }}
    </button>
  </div>
</div>

